body {
  display: flex;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  flex-direction: column;
  padding: 0;
  margin: 0;
  color: #3b4252;
  letter-spacing: 0;
}

.container {
  max-width: 960px;
  margin: auto;
  padding: 1rem;
}

header {
  border-bottom: 1px solid #eee;
}

header a {
  color: inherit;
  text-decoration: none;
}

header a:hover {
  text-decoration: underline;
}

nav ul {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}

nav li.title {
  flex-grow: 5;
  text-align: left;
  font-weight: bold;
  font-size: 1.4rem;
  color: #3b4252;
}

nav li {
  flex-grow: 1;
  align-self: center;
  text-align: right;
  color: #4c566a;
}

.fa-heart {
  color: deeppink;
}

main {
  flex: 1;
}

footer {
  margin-top: 4rem;
  border-top: 1px solid #eee;
}

h1 {
  margin-top: 4rem;
  font-weight: normal;
}

i {
  margin-right: 0.5rem;
}

a {
  color: #5e81ac;
}

a:hover {
  color: #81a1c1;
  text-decoration: underline;
}

table {
  margin-left: 0;
}

td {
  border: 0;
  padding: 0 1em 0.5em 0;
}

td:first-child {
  width: 1%;
  white-space: nowrap;
}

ul {
  list-style-position: inside;
  padding-left: 0;
}

li {
  list-style-type: none;
  margin-bottom: 0.2rem;
}

code {
  padding: 0.2rem;
  margin: 0rem 0.2rem;
  border-radius: 0.2rem;
  background: #e5e9f0;
}
