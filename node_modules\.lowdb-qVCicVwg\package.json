{"name": "lowdb", "version": "1.0.0", "description": "Small JSON database for Node, Electron and the browser. Powered by Lodash.", "keywords": ["flat", "file", "local", "database", "storage", "JSON", "lodash", "localStorage", "electron", "embed", "embeddable"], "main": "./lib/main.js", "scripts": {"test": "jest && npm run lint", "lint": "eslint . --ignore-path .gitignore", "fix": "npm run lint -- --fix", "prepublishOnly": "npm run build && pkg-ok", "build": "npm run build:lib && npm run build:dist", "build:lib": "rimraf lib && babel src --out-dir lib && npm run mvAdapters", "build:dist": "rimraf dist && webpack && webpack -p", "mvAdapters": "rimraf adapters && mv lib/adapters .", "precommit": "npm test"}, "repository": {"type": "git", "url": "git://github.com/typicode/lowdb.git"}, "author": "Typicode <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/typicode/lowdb/issues"}, "homepage": "https://github.com/typicode/lowdb", "dependencies": {"graceful-fs": "^4.1.3", "is-promise": "^2.1.0", "lodash": "4", "pify": "^3.0.0", "steno": "^0.4.1"}, "devDependencies": {"babel-cli": "^6.2.0", "babel-eslint": "^7.0.0", "babel-jest": "^20.0.3", "babel-loader": "^7.1.1", "babel-polyfill": "^6.9.1", "babel-preset-env": "^1.6.0", "babel-register": "^6.9.0", "delay": "^2.0.0", "eslint": "^4.5.0", "eslint-config-prettier": "^2.3.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.6.1", "eslint-plugin-node": "^5.1.0", "eslint-plugin-prettier": "^2.1.2", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "husky": "^0.14.3", "jest": "^20.0.4", "lodash-id": "^0.14.0", "mv": "^2.1.1", "pkg-ok": "^1.0.1", "prettier": "^1.5.2", "ramda": "^0.24.1", "regenerator-runtime": "^0.11.0", "rimraf": "^2.5.4", "sinon": "^3.2.1", "tempfile": "^2.0.0", "webpack": "^3.3.0"}, "engines": {"node": ">=4"}}