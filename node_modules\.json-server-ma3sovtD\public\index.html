<html>
  <head>
    <link
      rel="stylesheet"
      href="https://use.fontawesome.com/releases/v5.8.2/css/all.css"
      integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="style.css" />
    <title>JSON Server</title>
  </head>

  <body>
    <header>
      <div class="container">
        <nav>
          <ul>
            <li class="title">
              JSON Server
            </li>
            <li>
              <a href="https://github.com/users/typicode/sponsorship">
                <i class="fas fa-heart"></i>GitHub Sponsors
              </a>
            </li>
            <li>
              <a href="https://my-json-server.typicode.com">
                <i class="fas fa-burn"></i>My JSON Server
              </a>
            </li>
            <li>
              <a href="https://thanks.typicode.com">
                <i class="far fa-laugh"></i>Supporters
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>
    <main>
      <div class="container">
        <h1>Congrats!</h1>
        <p>
          You're successfully running JSON Server
          <br />
          ✧*｡٩(ˊᗜˋ*)و✧*｡
        </p>

        <div id="resources"></div>

        <p>
          To access and modify resources, you can use any HTTP method:
        </p>
        <p>
          <code>GET</code>
          <code>POST</code>
          <code>PUT</code>
          <code>PATCH</code>
          <code>DELETE</code>
          <code>OPTIONS</code>
        </p>

        <div id="custom-routes"></div>

        <h1>Documentation</h1>
        <p>
          <a href="https://github.com/typicode/json-server">
            README
          </a>
        </p>
      </div>
    </main>

    <footer>
      <div class="container">
        <p>
          To replace this page, create a
          <code>./public/index.html</code> file.
        </p>
      </div>
    </footer>

    <script src="script.js"></script>
  </body>
</html>
